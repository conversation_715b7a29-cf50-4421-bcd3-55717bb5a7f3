import request from "@/assets/js/request";
import store from "@/store";
import util from "@/assets/js/public";
// 查询
export function selectAll(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsUser/selectAll?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 添加
export function userAdd(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsUser/add`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 编辑
export function userEdit(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsUser/edit`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 删除
export function userDel(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsUser/deleteNew?id=${params.id}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 下载模板
export function downloadTemplate(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsUser/downloadTemplate`,
      contentType: "application/json; charset=utf-8",
      responseType: "blob"
  });
}

// 工单查询
export function getQueryPage(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usApplication/getQueryPage?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 导出
export function exportQuery(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usApplication/exportQuery`,
        contentType: "application/json; charset=utf-8",
        data: params,
        responseType: "blob"
    });
}
// 统计表格
export function statisticsQuery(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usApplication/statisticsQuery?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 统计表格数量
export function statisticsRate(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usApplication/statisticsRate`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 统计表格进度
export function statisticsRank(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usApplication/statisticsRank`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 统计表格导出
export function exportTJ(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usApplication/export`,
        contentType: "application/json; charset=utf-8",
        data: params,
        responseType: "blob"
    });
}

