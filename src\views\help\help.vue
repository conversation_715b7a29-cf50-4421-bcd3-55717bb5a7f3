<template>
  <div class="warp">
    <center>
      <font color="red" size="5"><b>领航计划创新管理平台帮助手册</b></font>
      <!-- <font color="red" size="4">v1.0.3.0</font> -->
    </center>
    <div class="clsCaption">
      <font color="blue">简介</font>
    </div>
    <p>
      &nbsp;&nbsp;&nbsp;&nbsp;
      “领航计划”创新周报线上闭环管理模块是为三门峡设计的数字化工作管理平台，旨在实现创新工作全流程线上化、透明化与数据化管理。该系统聚焦于创新内容提报、多级审批、任务转办、绩效评估及数据权限控制，覆盖从员工创新提案到管理层决策的完整闭环，助力提升组织创新效率与协同能力。
    </p>
    <div class="clsCaption">
      <font color="blue">流程介绍</font>
    </div>
    
    <!-- <img src="images/flow_chart_new.gif" width="700" height="900" border="0" /><br> -->
    <!-- <div class="clsCaption">
      <font color="blue">菜单介绍</font>
    </div> -->
    <ul>
      <li>1、提案上报：员工填写创新周报并提交至所属条线主管。</li>
      <li>2、条线初审：四条线主管审核内容可行性，通过后提交至地市管理层。</li>
      <li>3、管理层终审：批准立项、驳回或转办至其他部门执行。</li>
      <li>4、任务执行：责任部门落实创新方案，定期反馈进展。</li>
      <li>5、评分统计：系统根据预设维度（如创新性、落地效果）自动生成评估报表。</li>
    </ul>
    <div class="clsCaption">
      <font color="blue">流程图</font>
    </div>
    <ul>
      <li>1.流程整体逻辑架构设计：</li>
      <li>提案提交：流程起点，员工发起创新内容。</li>
      <li>两级审批：条线主管过滤低效提案，管理层决策最终流向。</li>
      <li>任务闭环：执行部门完成创新后，系统自动触发评分与报表生成。</li>
      <li>权限隔离：各部门管理员仅可访问授权数据。</li>
    </ul>
    <img src="./images/process.png" width="438" height="725" border="0" /><br>
  </div>
</template>
<script>
import store from "@/store";
import util from "@/assets/js/public";
export default {
  name: "help",
  data() {
    return {
    }
  },
  created() {
  }
};
</script>
<style scoped>
.warp {
  padding: 10px;
  font: 16px "宋体";
  line-height: 200%;
}
.clsCaption {
  font: bold 20px "宋体";
  line-height: 200%;
}
</style>