<template>
  <div :class="gps.location ? 'w99' : 'p10'">
    <process-btn
      ref="processBtn"
      :gps="gps"
      :processBtn="processBtn"
      :formData="appFormValue"
      :dialogClose="dialogClose"
      :on-ok="handleDoFun"
    ></process-btn>

    <!-- 业务表单 -->
    <div class="message tableForm">
      <div class="orderTitle" style="font-weight: 700">创新内容上报</div>
      <sb-el-form
        ref="appForm"
        :form="appForm"
        v-model="appFormValue"
        :disabled="appForm.formDisabled"
        @uploadFileList="uploadFileList"
        :on-ok="handleDoFun"
      >
      </sb-el-form>
    </div>
  </div>
</template>
<script>
import store from "@/store";
import util from "@/assets/js/public";
import ProcessBtn from "@/components/Process/ProcessBtn";
import { getDictList } from "@/api/public";
import { deleteDraft } from "@/api/process";
import {
  getProcessInfo,
  getFormDetail,
  saveDraft,
  startProcess,
} from "@/api/apply/application";
import { uploadProcessFiles } from "@/api/public";

let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  // applyUser: store.getters.user.truename,
  // applyUserName: store.getters.user.username,
  // belongCompanyName: store.getters.user.belongCompanyName,
  // belongDepartmentName: store.getters.user.belongDepartmentName,
  // applyTime: util.getNow(),
  blank: "blank",
};

export default {
  name: "application",
  components: { ProcessBtn },
  props: {
    href: {
      type: Object,
      default() {
        return {};
      },
    },
    // 关闭
    dialogClose: {
      type: Function,
    },
  },
  data() {
    return {
      gps: this.href,
      processDefKey: "",
      processBtn: {
        optClose: false,
      },

      processD: false,
      pnKey: 0,
      clickFlag: true, //防止多次点击

      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      // 业务表单
      initValue: {},
      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: false,
        labelWidth: "150px",
        inline: true,
        formItemList: [
          {
            class: "c12",
            label: "创新内容名称",
            key: "title",
            type: "input",
            placeholder: "请务必填写项目完整名称  如：XXXX项目",
            rule: { required: true },
          },
          {
            class: "c4",
            label: "上报人",
            key: "applyUserName",
            type: "input",
            disabled: true,
            rule: { required: false },
          },
          {
            class: "c4",
            label: "申请人OA",
            key: "applyUser",
            type: "input",
            disabled: true,
            show: false,
          },
          {
            class: "c4",
            label: "上报人电话",
            key: "applyPhone",
            type: "input",
            disabled: true,
            rule: { required: false },
          },
          {
            class: "c4",
            label: "上报人单位",
            key: "companyName",
            type: "input",
            disabled: true,
            rule: { required: false },
          },
          {
            class: "c4",
            label: "上报人部门名称",
            key: "deptName",
            type: "input",
            disabled: true,
            rule: { required: false },
          },

          {
            class: "c4",
            label: "创新条线分类",
            key: "lineType",
            type: "select",
            dictType: "lineType",
            rule: { required: true },
          },
          {
            class: "c4",
            label: "创新内容打分",
            key: "score",
            type: "input",
            show: false,
            rule: { required: false,type:'zinteger'},
            placeholder:'最高分10分'
          },
          {
            class: "c4",
            label: " ",
            key: "_blank",
            type: "input",
            placeholder: " ",
            disabled: true,
            show: true,
          },
          {
            class: "c12",
            label: "创新内容",
            key: "remark",
            type: "input",
            inputType: "textarea",
            rule: { required: true, maxlength: 500 },
          },
          {
            class: "c12",
            label: "附件",
            key: "formFiles",
            type: "sbUpload",
            btnText: "+",
            fun: "uploadFileList",
            listType: "text",
            multiple: true,
          },
        ],
      },
      taskTypeList: [],
      workNatureList: [],
      // 频次
      num1: 1,
      num2: 1,
      frequencyList: [
        { label: "日", value: "日" },
        { label: "周", value: "周" },
        { label: "月", value: "月" },
        { label: "年", value: "年" },
      ],
      frequencyValue: "日",
    };
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps, query);
    // console.log("gps", JSON.parse(JSON.stringify(this.gps)));

    this.initValue = {
      applyUser: this.$store.getters.user.username,
      applyUserName: this.$store.getters.user.truename,
      companyName:
        this.$store.getters.user.belongCompanyTypeDictValue == "03"
          ? this.$store.getters.user.belongCompanyNameParent
          : this.$store.getters.user.belongCompanyName,
      deptName: this.$store.getters.user.belongDepartmentName,
      applyTime: this.nowTime,
      applyPhone: this.$store.getters.user.preferredMobile,
    };
    this.appFormValue = Object.assign(defaultAppFormValue, this.initValue);

    this.initFun(); //初始化
  },
  methods: {
    // 初始化
    initFun() {
        this.processDefKey = 'Process_1752045480713';
      this.gps.processDefKey = this.processDefKey ;

      // 起草及草稿不显示“工单编号”
      // if(!this.gps.location || this.gps.location=="wgrcsw.start"){
      // 	var index = this.appForm.formItemList.findIndex(item => item.key==="workNumber");
      // 	if(index > -1){
      // 		this.appForm.formItemList[index].show = false;
      // 	}
      // 	this.appFormValue = JSON.parse(JSON.stringify(this.appFormValue));
      // }
      // 加载表单
      if (this.gps.location || (this.gps.action && this.gps.action == "read")) {
        this.loadForm();
      }
    },

    // 获取工单详情
    loadForm() {
      console.log(this.gps)
      var data = {
        pmInsId: this.gps.pmInsId,
        processDefKey: this.gps.processDefKey,
      };
      getFormDetail(data).then((res) => {
        this.appFormValue = res.data;
        // 表单禁用
        if (
          this.gps.type == "join" ||
          (this.gps.type == "task" && this.gps.location != "lhjh.start")
        ) {
          if(this.gps.location == "Activity_0fgnxyf" && this.gps.type == 'task'){
             this.appForm.formItemList.forEach((el) => {
              if(el.key == 'title' || el.key == 'lineType' ||  el.key == 'remark'){
                el.disabled = true
              }
             })

          }else{
            this.appForm.formDisabled = true;
          }
           this.appForm.formItemList.forEach((el) => {
            if (el.type == "sbUpload") {
              el.disabled = true;
            }
            
          });
        }
        
        if (
          this.gps.location == "Activity_0jhmn2u" ||
          this.gps.location == "Activity_093kkly" ||
          this.gps.location == "Activity_0fgnxyf" || (this.gps.type =='join' && this.appFormValue.score)
        ) {
          this.appForm.formItemList.forEach((el) => {
            if (el.key == "_blank") {
              el.show = false;
            }
            if (el.key == "score") {
              el.show = true;
              if (this.gps.location == "Activity_0fgnxyf" && this.gps.type == 'task') {
                el.rule.required  = true;
                el.disabled = false
              }
            }
          });
        } else {
          this.appForm.formItemList.forEach((el) => {
            if (el.key == "_blank") {
              el.show = true;
            }
            if (el.key == "score") {
              el.show = false;
            }
          });
        }
       
      });
    },
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData)
        .then((res) => {
          obj.content.onSuccess(res, obj.content.file, []);
        })
        .catch((error) => {
          obj.content.onError();
        });
    },
    // 重置表单
    handleFormReset() {
      this.appFormValue = Object.assign(defaultAppFormValue, this.initValue);
    },

    beforeSubmit() {},

    // 流转下一步
    handleNextBtn() {
      this.$refs["appForm"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
          this.$refs["processBtn"].doProcessNext();
        }
      });
    },

    // 保存草稿
    handleSaveDraft() {
      this.$refs["appForm"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
          if (this.clickFlag) {
            this.clickFlag = false;

            saveDraft({
              processDefKey: this.processDefKey,
              title: this.appFormValue.title,
              formData: this.appFormValue,
            })
              .then((res) => {
                this.clickFlag = true;
                if (!this.gps.location) {
                  this.$router.push({ name: "processDraft" });
                } else {
                  this.dialogClose();
                }
              })
              .catch((err) => {
                this.clickFlag = true;
              });
          }
        }
      });
    },

    // 废除草稿
    handleAbolish() {
      if (this.clickFlag) {
        this.clickFlag = false;
        deleteDraft({
          pmInsId: this.gps.pmInsId,
          processDefKey: this.processDefKey,
        })
          .then((res) => {
            this.clickFlag = false;
            this.dialogClose();
          })
          .catch((err) => {
            this.clickFlag = true;
          });
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n;
      if (obj) {
        n = this[obj[fun]].call(this, obj, data);
      } else {
        n = this[fun].call(this, data);
      }
      return n;
    },
  },
};
</script>
<style>
.w99 {
  width: 99%;
  margin: 0 auto;
}

.p10 {
  padding: 15px;
}

.tip {
  font-size: 13px;
  color: #a5a5a5;
  margin: 20px 0 10px;
}

.frequencyBox {
  display: flex;
  justify-content: space-around;
}

.frequencyBox p {
  width: 15%;
  line-height: 32px;
  text-align: center;
}

.frequencyBox > .el-input {
  width: 24%;
}

.frequencyBox > .el-input .el-input__inner {
  padding: 0 4px;
  text-align: center;
  border: 1px solid #dcdfe6;
}

.frequencyBox > .el-select {
  width: 29%;
}

.frequencyBox > .el-select .el-input__inner {
  padding: 0 4px 0 6px;
  border: 1px solid #dcdfe6;
}
</style>