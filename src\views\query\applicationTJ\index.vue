<template>
  <div class="app-container">
    <div class="tableQueryDD tableQueryForm tableContent flex">
      <sb-el-form
        :form="queryForm"
        v-model="listQuery"
        :disabled="queryForm.formDisabled"
        :on-ok="handleDoFun"
        :from="true"
      ></sb-el-form>
      <div style="width: 300px; margin-bottom: -2px">
        <el-button type="primary" size="small" @click="searchList()"
          >查询</el-button
        >
        <el-button type="primary" size="small" @click="handleExport()"
          >导出</el-button
        >
      </div>
    </div>

    <div class="card">
      <div class="item">
        <div class="item-text">
          各部门提交的创新数量：
          <span class="item-count">{{ rateInfo.total }}</span>
          个
        </div>
      </div>
      <div class="item">
        <div class="item-text">
          创新完结的数量：
          <span class="item-count">{{ rateInfo.end }}</span>
          个
        </div>
      </div>
      <div class="item">
        <div class="item-text">
          创新完结率 完结量/创新量：
          <span class="item-count" >{{ !isNaN(Number(rateInfo.rate)) ? (Number(rateInfo.rate) * 100).toFixed(2) : 0}}</span>
          %
        </div>
      </div>
      <div class="item">
        <div class="item-text">
          创新正在复制推广的数量：
          <span class="item-count">{{ rateInfo.promotion }}</span>
          个
        </div>
      </div>
      <div class="item">
        <div class="item-text">
          创新内容总得分：
          <span class="item-count">{{ rateInfo.score }}</span>
          分
        </div>
      </div>
    </div>
    <!-- 进度条表格 -->
    <div class="mian-box">
      <div class="left">
        <div class="item" v-for="(item, index) in rankList" :key="index">
          <div class="item-name">
            <div class="xh">{{ index + 1 }}</div>
            <div class="name">{{ item.applyUserName }}</div>
          </div>
          <el-progress
            :percentage="handelprocess(item.score)"
            :stroke-width="6"
            :format="format"
            :color="hanleColor(index)"
          ></el-progress>
        </div>
      </div>
      <div class="right">
        <sb-el-table
          :table="table"
          @getList="getList"
          @handleTodo="handleTodo"
          :on-ok="handleDoFun"
        >
          <template v-slot:BUSINESS_TITLE="{ obj }">
            <span class="toDetail" @click="handleTodo(obj)">{{
              obj.row.BUSINESS_TITLE
            }}</span>
          </template>
          <template v-slot:CREATED_TIME="{ obj }">
            <span>{{
              util.getTimeDate(obj.row.CREATED_TIME, "yyyy-MM-dd HH:mm:ss")
            }}</span>
          </template>
          <template v-slot:lineType="{ obj }">
            <span>{{ handleType(obj.row.lineType) }}</span>
          </template>
        </sb-el-table>
      </div>
    </div>

    <!-- 工单详情 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="viewD"
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
      :fullscreen="true"
    >
      <work-order
        :key="cKey"
        :gps="gps"
        :dialogClose="dialogClose"
      ></work-order>
    </el-dialog>
  </div>
</template>
<script>
import WorkOrder from "@/components/WorkOrder";
import { statisticsRank, statisticsRate, statisticsQuery,exportTJ } from "@/api/home";
import util from "@/assets/js/public";
import { getDictList } from "@/api/public";

export default {
  name: "applicationQuery",
  components: { WorkOrder },
  data() {
    return {
      viewD: false,

      gps: {
        type: "join",
        location: "",
        pmInsType: "",
      },
      dialogTitle: "",

      cKey: 0,
      listQuery: {},
      queryForm: {
        formDisabled: false,
        inline: true,
        labelWidth: "120px",
        formItemList: [
          {
            class: "",
            label: "单位名称",
            key: "companyName",
            type: "select",
            dictType: "compName",
            // props:{value: 'id', label: 'companyName',},changeFun:'handleCity'
          },
          {
            class: "",
            label: "创新条线分类",
            key: "lineType",
            type: "select",
            dictType: "lineType",

          },
          {
            class: "",
            label: "申请人姓名",
            key: "applyUserName",
            type: "input",
          },
          {
            class: "",
            label: "时间",
            key: "applyTime",
            type: "date",
            subtype: "daterange",
            datetimerange: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rangeName: ["startDate", "endDate"],
          },
          {
            class: "",
            label: "近两周数据",
            key: "twomonth",
            type: "switch",
            activeValue: false,
            changeFun: "handleSwitch",
          },
          {
            class: "",
            label: "当月上报数据",
            key: "month",
            type: "switch",
            activeValue: false,
            changeFun: "handleSwitch",
          },
          {
            class: "",
            label: "本年上报数据",
            key: "year",
            type: "switch",
            activeValue: false,
            changeFun: "handleSwitch",
          },
        ],
      },
      table: {
        modulName: "applicationQuery-工单查询", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "120px",
          formItemList: [],
        },
        tr: [
          {
            id: "belongCompanyName",
            label: "上报人单位名称",
            prop: "belongCompanyName",
            width: 200,
          },
          {
            id: "belongDepartmentName",
            label: "上报人部门名称",
            prop: "belongDepartmentName",
            width: 200,
          },
          {
            id: "lineType",
            label: "创新条线分类",
            prop: "lineType",
            show: "template",
            template: "lineType",
          },
          {
            id: "applyUserName",
            label: "上报人",
            prop: "applyUserName",
            width: 200,
          },

          {
            id: "score",
            label: "得分",
            prop: "score",
            width: 200,
          },

          // {id: "CREATED_TIME",label: "申请时间",prop: "CREATED_TIME",width: 200, show: "template" },
        ],
        // hasSetup:true,
        // setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "80",
          fixed: "right",
          data: [{ id: "handleTodo", name: "查看", fun: "handleTodo" }],
        },
        hasOtherQueryBtn: false, //是否有其他操作
        otherQueryBtn: {
          data: [
            {
              id: "export",
              type: "success",
              name: "导出",
              fun: "handleExport",
            },
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      lineTypeList: [],
      rateInfo: {},
      rankList: [],
    };
  },
  created() {
    this.getLineType();
    this.getRate();
    this.getRank();
    this.getList();
  },
  methods: {
    handleType(value) {
      let name = "";
      this.lineTypeList?.forEach((el) => {
        if (el.value == value) {
          name = el.name;
        }
      });
      return name;
    },
    getLineType() {
      getDictList("lineType").then((res) => {
        this.lineTypeList = res.data;
      });
    },
    handleSwitch(obj, value) {
      console.log(obj, value);
      if (obj.key == "twomonth" && value) {
        this.listQuery.month = false;
        this.listQuery.year = false;
      }
      if (obj.key == "month" && value) {
        this.listQuery.twomonth = false;
        this.listQuery.year = false;
      }
      if (obj.key == "year" && value) {
        this.listQuery.month = false;
        this.listQuery.twomonth = false;
      }
    },
    getRate() {
      if (this.listQuery) {
        if (this.listQuery.twomonth) {
          this.listQuery.queryType = "1";
        }
        if (this.listQuery.month) {
          this.listQuery.queryType = "2";
        }
        if (this.listQuery.year) {
          this.listQuery.queryType = "3";
        }
      }
      statisticsRate(this.listQuery).then((res) => {
        console.log(res, "rate");
        this.rateInfo = res.data;
      });
    },
    getRank() {
      if (this.listQuery) {
        if (this.listQuery.twomonth) {
          this.listQuery.queryType = "1";
        }
        if (this.listQuery.month) {
          this.listQuery.queryType = "2";
        }
        if (this.listQuery.year) {
          this.listQuery.queryType = "3";
        }
      }
      statisticsRank(this.listQuery).then((res) => {
        console.log(res, "rabk");
        this.rankList = res.data;
      });
    },
    handelprocess(value) {
      return value * 10 || 0;
    },
    format(percentage) {
      console.log(percentage);
      return `创新内容打分：${percentage / 10}分`;
    },
    hanleColor(index) {
      console.log(index, "11");
      if (index === 0) {
        return "#FC619D";
      } else if (index === 1) {
        return "#FF904D";
      } else if (index === 2) {
        return "#48BFE3";
      } else {
        return "#1990FF";
      }
    },
    searchList() {
      if (this.listQuery && !this.listQuery.applyTime) {
        this.listQuery.startDate = "";
        this.listQuery.endDate = "";
      }
      this.getRate();
      this.getRank();
      this.getList();
    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      if (this.listQuery) {
        if (this.listQuery.twomonth) {
          this.listQuery.queryType = "1";
        }
        if (this.listQuery.month) {
          this.listQuery.queryType = "2";
        }
        if (this.listQuery.year) {
          this.listQuery.queryType = "3";
        }
      }
      var params = Object.assign(
        listQuery || this.table.listQuery,
        this.listQuery
      );
      statisticsQuery(params)
        .then((res) => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
        })
        .catch((err) => {
          this.table.loading = false;
        });
    },

    // 查看
    handleTodo(obj) {
      // 参数
      this.gps = {
        type: "join",
        location: `${process.env.VUE_APP_APPCODE}.`,
        pmInsId: obj.row.pmInsId,
        pmInsType: obj.row.pmInsType,
        processInstId: obj.row.processInstId,
      };
      var th = this.util.appNameTH(obj.row.pmInsType);
      this.dialogTitle = th.type + (obj.row.RECEIPTTILE || "") + "-查看";
      this.cKey++;
      this.viewD = true;
    },

    // 导出
    handleExport() {
    if (this.listQuery) {
        if (this.listQuery.twomonth) {
          this.listQuery.queryType = "1";
        }
        if (this.listQuery.month) {
          this.listQuery.queryType = "2";
        }
        if (this.listQuery.year) {
          this.listQuery.queryType = "3";
        }
      }
      exportTJ(this.listQuery).then((res) => {
        if (res.data) {
          this.util.blobDownload(res.data, res.filename);
        } else {
          this.$message({
            message: "导出失败",
            type: "warning",
            duration: 1500,
          });
        }
      });
    },

    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n;
      if (obj) {
        n = this[obj[fun]].call(this, obj, data);
      } else {
        n = this[fun].call(this, data);
      }
      return n;
    },
  },
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header {
  text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;
}
::v-deep .el-dialog__title {
  color: black !important;
  font-size: 15.5px;
}
::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: black;
}
.card {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 20px 0px 40px 0px;
  justify-content: space-around;
}
.card .item {
  padding: 5px 25px;
  background-color: rgba(243, 249, 255, 1);
  border: 1px solid rgba(242, 242, 242, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}
.item-text {
  font-size: 18px;
  color: #333333;
  letter-spacing: normal;
}
.item-count {
  font-size: 28px;
}
.mian-box {
  width: 100%;
  padding: 0px 20px 20px 20px;
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
}
.mian-box .left {
  width: 40%;
}
.mian-box .right {
  width: 66%;
  margin-left: 30px;
}
.mian-box .item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.mian-box .item-name {
  margin-right: 5px;
  display: flex;
  align-items: center;
}
.mian-box .item-name .xh {
  text-align: left;
  width: 23px;
  height: 23px;
  text-align: center;
  flex-shrink: 0;
  font-size: 14.4px;
  color: rgb(96, 98, 102);
  line-height: 23px;
}
.mian-box .item:nth-child(1) .xh {
  background: #ffe8ec;
  border-radius: 50%;
  color: #fd8fa4;
}
.mian-box .item:nth-child(2) .xh {
  background: #ffeace;
  border-radius: 50%;
  color: #ffaf5f;
}
.mian-box .item:nth-child(3) .xh {
  background: #e1f7f3;
  border-radius: 50%;
  color: #62d2c0;
}
.mian-box .item-name .name {
  margin-left: 5px;
  font-size: 14.4px;
  color: rgb(96, 98, 102);
}
::v-deep .el-progress {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-between;
  margin-left: 10px;
}
::v-deep .el-progress-bar {
  width: 80%;
  flex: 1;
}
::v-deep .el-progress__text {
  width: 130px;
  flex-shrink: 0;
  margin-left: 30px;
}
</style>